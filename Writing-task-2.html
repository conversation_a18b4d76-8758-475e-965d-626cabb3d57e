<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IELTS Writing Task 2 - Digital vs Print Media</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .lesson-content {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .navigation {
            background: #2c3e50;
            padding: 15px;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        .nav-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .nav-btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .nav-btn.active {
            background: #e74c3c;
        }

        .step {
            display: none;
            padding: 40px;
            animation: fadeIn 0.5s ease-in;
        }

        .step.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .step h2 {
            color: #2c3e50;
            font-size: 2em;
            margin-bottom: 20px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }

        .question-box {
            background: #f8f9fa;
            border-left: 5px solid #3498db;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
            font-style: italic;
            font-size: 1.1em;
        }

        .tip-box {
            background: #e8f5e8;
            border: 2px solid #27ae60;
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
        }

        .tip-box h4 {
            color: #27ae60;
            margin-bottom: 10px;
        }

        .warning-box {
            background: #fff3cd;
            border: 2px solid #ffc107;
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
        }

        .warning-box h4 {
            color: #856404;
            margin-bottom: 10px;
        }

        .example-box {
            background: #f0f8ff;
            border: 2px solid #4169e1;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }

        .example-box h4 {
            color: #4169e1;
            margin-bottom: 15px;
        }

        .structure-visual {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
            margin: 20px 0;
        }

        .paragraph-box {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            padding: 15px;
            border-radius: 8px;
            position: relative;
        }

        .paragraph-box h5 {
            color: #495057;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .interactive-section {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 2px dashed #6c757d;
        }

        .practice-input {
            width: 100%;
            min-height: 100px;
            padding: 15px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-family: inherit;
            font-size: 16px;
            resize: vertical;
        }

        .practice-input:focus {
            outline: none;
            border-color: #3498db;
        }

        .check-btn {
            background: #27ae60;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            margin-top: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .check-btn:hover {
            background: #219a52;
            transform: translateY(-2px);
        }

        .feedback {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            display: none;
        }

        .feedback.show {
            display: block;
        }

        .feedback.positive {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .feedback.negative {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .vocabulary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .vocab-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }

        .vocab-card h5 {
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .progress-bar {
            background: #e9ecef;
            height: 8px;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            background: linear-gradient(90deg, #3498db, #2ecc71);
            height: 100%;
            width: 0%;
            transition: width 0.5s ease;
        }

        .next-btn, .prev-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .next-btn:hover, .prev-btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .prev-btn {
            background: #95a5a6;
        }

        .prev-btn:hover {
            background: #7f8c8d;
        }

        ul, ol {
            margin-left: 20px;
            margin-bottom: 15px;
        }

        li {
            margin-bottom: 8px;
        }

        .highlight {
            background: #fff3cd;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .step {
                padding: 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .navigation {
                flex-direction: column;
                align-items: center;
            }

            .nav-btn {
                width: 200px;
                margin: 5px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>IELTS Writing Task 2</h1>
            <p>Digital vs Print Media - Step-by-Step Guide for Band 5-6.0</p>
        </div>

        <div class="lesson-content">
            <div class="navigation">
                <button class="nav-btn active" onclick="showStep(0)">Introduction</button>
                <button class="nav-btn" onclick="showStep(1)">Step 1: Analyze</button>
                <button class="nav-btn" onclick="showStep(2)">Step 2: Plan</button>
                <button class="nav-btn" onclick="showStep(3)">Step 3: Introduction</button>
                <button class="nav-btn" onclick="showStep(4)">Step 4: Body Paragraphs</button>
                <button class="nav-btn" onclick="showStep(5)">Step 5: Conclusion</button>
                <button class="nav-btn" onclick="showStep(6)">Step 6: Review</button>
                <button class="nav-btn" onclick="showStep(7)">Sample Essay</button>
                <button class="nav-btn" onclick="showStep(8)">Vocabulary</button>
            </div>

            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>

            <!-- Introduction Step -->
            <div class="step active" id="step0">
                <h2>📚 Introduction to IELTS Writing Task 2</h2>

                <div class="question-box">
                    <strong>Today's Question:</strong><br>
                    "In the future, nobody will buy printed newspapers or books because they will be able to read everything they want online without paying.<br><br>
                    To what extent do you agree or disagree with this statement?"
                </div>

                <h3>What is this question asking?</h3>
                <p>This is an <span class="highlight">opinion essay</span> where you need to:</p>
                <ul>
                    <li>Give your opinion about whether people will stop buying printed newspapers and books</li>
                    <li>Explain your reasons with examples</li>
                    <li>Write about 250 words</li>
                    <li>Take about 40 minutes</li>
                </ul>

                <div class="tip-box">
                    <h4>💡 For Band 5-6.0 Students</h4>
                    <p>Don't worry about being perfect! Focus on:</p>
                    <ul>
                        <li>Clear structure (introduction, 2 body paragraphs, conclusion)</li>
                        <li>Simple but correct grammar</li>
                        <li>Basic linking words (however, because, for example)</li>
                        <li>Staying on topic</li>
                    </ul>
                </div>

                <h3>Key Question Types to Remember:</h3>
                <ul>
                    <li><strong>Agree/Disagree:</strong> "To what extent do you agree or disagree?"</li>
                    <li><strong>Discuss both views:</strong> "Discuss both views and give your opinion"</li>
                    <li><strong>Advantages/Disadvantages:</strong> "What are the advantages and disadvantages?"</li>
                    <li><strong>Problem/Solution:</strong> "What are the causes and solutions?"</li>
                </ul>

                <div class="warning-box">
                    <h4>⚠️ Common Mistake</h4>
                    <p>Don't just list ideas! Always explain WHY you think something and give examples.</p>
                </div>

                <button class="next-btn" onclick="showStep(1)">Start Step 1: Analyze the Question →</button>
            </div>

            <!-- Step 1: Analyze -->
            <div class="step" id="step1">
                <h2>🔍 Step 1: Analyze the Question (3 minutes)</h2>

                <div class="question-box">
                    "In the future, nobody will buy printed newspapers or books because they will be able to read everything they want online without paying.<br><br>
                    To what extent do you agree or disagree with this statement?"
                </div>

                <h3>Let's break this down:</h3>

                <div class="example-box">
                    <h4>🎯 Key Elements to Identify:</h4>
                    <ol>
                        <li><strong>Topic:</strong> Digital reading vs. printed newspapers and books</li>
                        <li><strong>Time:</strong> "In the future"</li>
                        <li><strong>Claim:</strong> "Nobody will buy printed materials"</li>
                        <li><strong>Reason given:</strong> "Free online content"</li>
                        <li><strong>Task:</strong> Agree or disagree + explain extent</li>
                    </ol>
                </div>

                <h3>Questions to ask yourself:</h3>
                <ul>
                    <li>Do I think ALL people will stop buying printed materials?</li>
                    <li>Are there advantages to printed books/newspapers?</li>
                    <li>What about older people or people without internet?</li>
                    <li>Is all online content really free?</li>
                </ul>

                <div class="interactive-section">
                    <h4>🤔 Your Turn: Quick Analysis</h4>
                    <p>Write your first thoughts about this topic (don't worry about grammar):</p>
                    <textarea class="practice-input" id="analysis-input" placeholder="Example: I think some people will still buy books because..."></textarea>
                    <button class="check-btn" onclick="checkAnalysis()">Check My Thinking</button>
                    <div class="feedback" id="analysis-feedback"></div>
                </div>

                <div class="tip-box">
                    <h4>💡 Tip for Band 5-6.0</h4>
                    <p>You don't need to completely agree OR completely disagree. You can say "I partly agree because..." This is often easier to write about!</p>
                </div>

                <div class="navigation-buttons">
                    <button class="prev-btn" onclick="showStep(0)">← Previous</button>
                    <button class="next-btn" onclick="showStep(2)">Next: Plan Your Essay →</button>
                </div>
            </div>

            <!-- Step 2: Plan -->
            <div class="step" id="step2">
                <h2>📝 Step 2: Plan Your Essay Structure (5 minutes)</h2>

                <p>A good plan saves time and helps you write a clear essay. Here's a simple structure for band 5-6.0:</p>

                <div class="structure-visual">
                    <div class="paragraph-box">
                        <h5>📖 Introduction (2-3 sentences)</h5>
                        <ul>
                            <li>Restate the topic in your own words</li>
                            <li>Give your opinion clearly</li>
                        </ul>
                    </div>

                    <div class="paragraph-box">
                        <h5>💪 Body Paragraph 1 (3-4 sentences)</h5>
                        <ul>
                            <li>Your main reason + explanation</li>
                            <li>Example or detail</li>
                        </ul>
                    </div>

                    <div class="paragraph-box">
                        <h5>🔄 Body Paragraph 2 (3-4 sentences)</h5>
                        <ul>
                            <li>Your second reason OR opposite view + your response</li>
                            <li>Example or detail</li>
                        </ul>
                    </div>

                    <div class="paragraph-box">
                        <h5>🎯 Conclusion (2 sentences)</h5>
                        <ul>
                            <li>Summarize your opinion</li>
                            <li>Final thought</li>
                        </ul>
                    </div>
                </div>

                <div class="example-box">
                    <h4>📋 Sample Plan for Our Topic:</h4>
                    <p><strong>My opinion:</strong> I partly disagree - some people will still buy printed materials</p>

                    <p><strong>Body 1:</strong> Why some people will stop buying printed materials</p>
                    <ul>
                        <li>Convenience and cost</li>
                        <li>Example: Young people prefer phones/tablets</li>
                    </ul>

                    <p><strong>Body 2:</strong> Why some people will continue buying printed materials</p>
                    <ul>
                        <li>Better reading experience, no eye strain</li>
                        <li>Example: Older people, students studying</li>
                    </ul>
                </div>

                <div class="interactive-section">
                    <h4>✏️ Create Your Plan</h4>
                    <p>Write your basic plan here:</p>
                    <textarea class="practice-input" id="plan-input" placeholder="My opinion: I agree/disagree because...

Body 1: First reason...
Example: ...

Body 2: Second reason...
Example: ..."></textarea>
                    <button class="check-btn" onclick="checkPlan()">Check My Plan</button>
                    <div class="feedback" id="plan-feedback"></div>
                </div>

                <div class="tip-box">
                    <h4>💡 Planning Tips</h4>
                    <ul>
                        <li>Keep it simple - 2 main ideas are enough</li>
                        <li>Think of real examples from your life or knowledge</li>
                        <li>Make sure your examples support your opinion</li>
                    </ul>
                </div>

                <div class="navigation-buttons">
                    <button class="prev-btn" onclick="showStep(1)">← Previous</button>
                    <button class="next-btn" onclick="showStep(3)">Next: Write Introduction →</button>
                </div>
            </div>

            <!-- Step 3: Introduction -->
            <div class="step" id="step3">
                <h2>🚀 Step 3: Write Your Introduction (5 minutes)</h2>

                <p>Your introduction should be simple and clear. For band 5-6.0, focus on these two parts:</p>

                <div class="structure-visual">
                    <div class="paragraph-box">
                        <h5>1️⃣ Paraphrase the Question</h5>
                        <p>Rewrite the main idea in your own words</p>
                    </div>
                    <div class="paragraph-box">
                        <h5>2️⃣ State Your Opinion</h5>
                        <p>Clearly say what you think</p>
                    </div>
                </div>

                <div class="example-box">
                    <h4>📝 Useful Introduction Phrases:</h4>
                    <p><strong>For paraphrasing:</strong></p>
                    <ul>
                        <li>"Some people believe that..."</li>
                        <li>"It is argued that..."</li>
                        <li>"Many think that in the future..."</li>
                    </ul>

                    <p><strong>For giving your opinion:</strong></p>
                    <ul>
                        <li>"I completely agree/disagree with this view."</li>
                        <li>"I partly agree with this statement."</li>
                        <li>"In my opinion, this is not entirely true."</li>
                    </ul>
                </div>

                <div class="example-box">
                    <h4>✅ Sample Introduction:</h4>
                    <p><em>"Some people believe that in the future, printed newspapers and books will disappear because everyone will read online content for free. <strong>I partly disagree with this view</strong> because although many people will choose digital reading, some will still prefer printed materials."</em></p>

                    <p><strong>Why this works:</strong></p>
                    <ul>
                        <li>✅ Paraphrases the question</li>
                        <li>✅ Clear opinion</li>
                        <li>✅ Simple language</li>
                        <li>✅ Sets up the essay structure</li>
                    </ul>
                </div>

                <div class="interactive-section">
                    <h4>✏️ Write Your Introduction</h4>
                    <p>Try writing your introduction here:</p>
                    <textarea class="practice-input" id="intro-input" placeholder="Some people believe that...

I agree/disagree because..."></textarea>
                    <button class="check-btn" onclick="checkIntroduction()">Check My Introduction</button>
                    <div class="feedback" id="intro-feedback"></div>
                </div>

                <div class="warning-box">
                    <h4>⚠️ Common Introduction Mistakes</h4>
                    <ul>
                        <li>Don't copy the question exactly</li>
                        <li>Don't write too much (2-3 sentences is enough)</li>
                        <li>Don't forget to give your opinion</li>
                        <li>Don't use "I will discuss..." (just state your opinion)</li>
                    </ul>
                </div>

                <div class="navigation-buttons">
                    <button class="prev-btn" onclick="showStep(2)">← Previous</button>
                    <button class="next-btn" onclick="showStep(4)">Next: Body Paragraphs →</button>
                </div>
            </div>

            <!-- Step 4: Body Paragraphs -->
            <div class="step" id="step4">
                <h2>💪 Step 4: Write Body Paragraphs (20 minutes)</h2>

                <p>Each body paragraph should have a clear structure. Here's the simple formula for band 5-6.0:</p>

                <div class="structure-visual">
                    <div class="paragraph-box">
                        <h5>1️⃣ Topic Sentence</h5>
                        <p>State your main point clearly</p>
                    </div>
                    <div class="paragraph-box">
                        <h5>2️⃣ Explanation</h5>
                        <p>Explain WHY this point is important</p>
                    </div>
                    <div class="paragraph-box">
                        <h5>3️⃣ Example</h5>
                        <p>Give a specific example or detail</p>
                    </div>
                    <div class="paragraph-box">
                        <h5>4️⃣ Link (optional)</h5>
                        <p>Connect back to your main opinion</p>
                    </div>
                </div>

                <div class="example-box">
                    <h4>📝 Useful Linking Words:</h4>
                    <div class="vocabulary-grid">
                        <div class="vocab-card">
                            <h5>To start paragraphs:</h5>
                            <p>Firstly, To begin with, One reason</p>
                        </div>
                        <div class="vocab-card">
                            <h5>To explain:</h5>
                            <p>This is because, The reason is, As a result</p>
                        </div>
                        <div class="vocab-card">
                            <h5>To give examples:</h5>
                            <p>For example, For instance, Such as</p>
                        </div>
                        <div class="vocab-card">
                            <h5>To show contrast:</h5>
                            <p>However, On the other hand, Although</p>
                        </div>
                    </div>
                </div>

                <div class="example-box">
                    <h4>✅ Sample Body Paragraph 1:</h4>
                    <p><em><strong>Firstly, many people will stop buying printed materials because digital reading is more convenient and cheaper.</strong> This is because online content can be accessed immediately from anywhere, and people do not need to go to shops or wait for delivery. <strong>For example,</strong> young people today prefer to read news on their smartphones during their commute rather than carrying heavy newspapers. <strong>As a result,</strong> the demand for printed newspapers will likely decrease significantly.</em></p>
                </div>

                <div class="example-box">
                    <h4>✅ Sample Body Paragraph 2:</h4>
                    <p><em><strong>However, some people will continue to buy printed books and newspapers because they provide a better reading experience.</strong> This is because reading from paper is easier on the eyes and helps people concentrate better than reading from screens. <strong>For instance,</strong> many students still prefer printed textbooks when studying for exams because they can make notes and remember information more easily. <strong>Therefore,</strong> printed materials will not disappear completely.</em></p>
                </div>

                <div class="interactive-section">
                    <h4>✏️ Write Your Body Paragraphs</h4>
                    <p><strong>Body Paragraph 1:</strong></p>
                    <textarea class="practice-input" id="body1-input" placeholder="Firstly, ... because ...

This is because ...

For example, ..."></textarea>

                    <p><strong>Body Paragraph 2:</strong></p>
                    <textarea class="practice-input" id="body2-input" placeholder="However, ... because ...

This is because ...

For instance, ..."></textarea>

                    <button class="check-btn" onclick="checkBodyParagraphs()">Check My Body Paragraphs</button>
                    <div class="feedback" id="body-feedback"></div>
                </div>

                <div class="tip-box">
                    <h4>💡 Body Paragraph Tips</h4>
                    <ul>
                        <li>Each paragraph = one main idea</li>
                        <li>Use simple, clear examples from real life</li>
                        <li>Don't worry about complex grammar - focus on clarity</li>
                        <li>Make sure each paragraph is 3-4 sentences</li>
                    </ul>
                </div>

                <div class="navigation-buttons">
                    <button class="prev-btn" onclick="showStep(3)">← Previous</button>
                    <button class="next-btn" onclick="showStep(5)">Next: Conclusion →</button>
                </div>
            </div>

            <!-- Step 5: Conclusion -->
            <div class="step" id="step5">
                <h2>🎯 Step 5: Write Your Conclusion (5 minutes)</h2>

                <p>Your conclusion should be short and simple. For band 5-6.0, just do these two things:</p>

                <div class="structure-visual">
                    <div class="paragraph-box">
                        <h5>1️⃣ Summarize Your Opinion</h5>
                        <p>Restate what you think in different words</p>
                    </div>
                    <div class="paragraph-box">
                        <h5>2️⃣ Final Thought (optional)</h5>
                        <p>Add a brief final comment or prediction</p>
                    </div>
                </div>

                <div class="example-box">
                    <h4>📝 Useful Conclusion Phrases:</h4>
                    <ul>
                        <li>"In conclusion, ..."</li>
                        <li>"To summarize, ..."</li>
                        <li>"Overall, I believe that ..."</li>
                        <li>"In my opinion, ..."</li>
                    </ul>
                </div>

                <div class="example-box">
                    <h4>✅ Sample Conclusion:</h4>
                    <p><em>"In conclusion, while many people will choose to read online content because it is convenient and free, I believe that printed newspapers and books will not disappear completely. Some people will always prefer the traditional reading experience that printed materials provide."</em></p>

                    <p><strong>Why this works:</strong></p>
                    <ul>
                        <li>✅ Summarizes the main opinion</li>
                        <li>✅ Uses different words from the introduction</li>
                        <li>✅ Short and clear (2 sentences)</li>
                        <li>✅ No new ideas introduced</li>
                    </ul>
                </div>

                <div class="interactive-section">
                    <h4>✏️ Write Your Conclusion</h4>
                    <textarea class="practice-input" id="conclusion-input" placeholder="In conclusion, ...

I believe that ..."></textarea>
                    <button class="check-btn" onclick="checkConclusion()">Check My Conclusion</button>
                    <div class="feedback" id="conclusion-feedback"></div>
                </div>

                <div class="warning-box">
                    <h4>⚠️ Common Conclusion Mistakes</h4>
                    <ul>
                        <li>Don't introduce new ideas</li>
                        <li>Don't just copy your introduction</li>
                        <li>Don't write too much (2 sentences is enough)</li>
                        <li>Don't use "In my essay I discussed..." (just restate your opinion)</li>
                    </ul>
                </div>

                <div class="navigation-buttons">
                    <button class="prev-btn" onclick="showStep(4)">← Previous</button>
                    <button class="next-btn" onclick="showStep(6)">Next: Review →</button>
                </div>
            </div>

            <!-- Step 6: Review -->
            <div class="step" id="step6">
                <h2>🔍 Step 6: Review and Check (5 minutes)</h2>

                <p>Always save time to check your essay! Here's a simple checklist for band 5-6.0:</p>

                <div class="example-box">
                    <h4>📋 Quick Review Checklist</h4>
                    <div class="structure-visual">
                        <div class="paragraph-box">
                            <h5>✅ Content Check</h5>
                            <ul>
                                <li>Did I answer the question?</li>
                                <li>Is my opinion clear?</li>
                                <li>Do I have 2 main ideas with examples?</li>
                                <li>Is my essay about 250 words?</li>
                            </ul>
                        </div>

                        <div class="paragraph-box">
                            <h5>✅ Structure Check</h5>
                            <ul>
                                <li>Introduction with opinion</li>
                                <li>2 body paragraphs</li>
                                <li>Conclusion</li>
                                <li>Clear paragraphs (new line for each)</li>
                            </ul>
                        </div>

                        <div class="paragraph-box">
                            <h5>✅ Language Check</h5>
                            <ul>
                                <li>Basic linking words used</li>
                                <li>Simple grammar (don't try complex if unsure)</li>
                                <li>Spelling of key words</li>
                                <li>Capital letters and periods</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="tip-box">
                    <h4>💡 Quick Grammar Check</h4>
                    <p>Look for these common mistakes:</p>
                    <ul>
                        <li><strong>Subject-verb agreement:</strong> "People think" not "People thinks"</li>
                        <li><strong>Articles:</strong> "The internet" not "Internet"</li>
                        <li><strong>Plural/singular:</strong> "Many people" not "Much people"</li>
                        <li><strong>Word order:</strong> "I completely agree" not "I agree completely"</li>
                    </ul>
                </div>

                <div class="warning-box">
                    <h4>⚠️ Don't Panic!</h4>
                    <p>If you find mistakes:</p>
                    <ul>
                        <li>Fix obvious errors (spelling, capital letters)</li>
                        <li>Don't rewrite everything - you don't have time</li>
                        <li>Cross out mistakes neatly and write corrections above</li>
                        <li>Remember: a clear, simple essay is better than a complex, confusing one</li>
                    </ul>
                </div>

                <div class="example-box">
                    <h4>🎯 Word Count Guide</h4>
                    <p>For band 5-6.0, aim for:</p>
                    <ul>
                        <li><strong>Introduction:</strong> 40-50 words</li>
                        <li><strong>Body Paragraph 1:</strong> 70-80 words</li>
                        <li><strong>Body Paragraph 2:</strong> 70-80 words</li>
                        <li><strong>Conclusion:</strong> 30-40 words</li>
                        <li><strong>Total:</strong> 250-280 words</li>
                    </ul>
                </div>

                <div class="interactive-section">
                    <h4>📝 Practice Review</h4>
                    <p>Paste your complete essay here for a quick check:</p>
                    <textarea class="practice-input" id="review-input" placeholder="Paste your complete essay here..."></textarea>
                    <button class="check-btn" onclick="reviewEssay()">Review My Essay</button>
                    <div class="feedback" id="review-feedback"></div>
                </div>

                <div class="navigation-buttons">
                    <button class="prev-btn" onclick="showStep(5)">← Previous</button>
                    <button class="next-btn" onclick="showStep(7)">Next: Sample Essay →</button>
                </div>
            </div>

            <!-- Step 7: Sample Essay -->
            <div class="step" id="step7">
                <h2>📄 Step 7: Complete Sample Essay</h2>

                <p>Here's a complete band 5-6.0 essay following all the steps we learned:</p>

                <div class="example-box">
                    <h4>✅ Band 5-6.0 Sample Essay (267 words)</h4>

                    <div class="paragraph-box">
                        <h5>📖 Introduction</h5>
                        <p><em>Some people believe that in the future, printed newspapers and books will disappear because everyone will read online content for free. <strong>I partly disagree with this view</strong> because although many people will choose digital reading, some will still prefer printed materials.</em></p>
                        <small>(47 words)</small>
                    </div>

                    <div class="paragraph-box">
                        <h5>💪 Body Paragraph 1</h5>
                        <p><em><strong>Firstly, many people will stop buying printed materials because digital reading is more convenient and cheaper.</strong> This is because online content can be accessed immediately from anywhere, and people do not need to go to shops or wait for delivery. <strong>For example,</strong> young people today prefer to read news on their smartphones during their commute rather than carrying heavy newspapers. <strong>As a result,</strong> the demand for printed newspapers will likely decrease significantly.</em></p>
                        <small>(78 words)</small>
                    </div>

                    <div class="paragraph-box">
                        <h5>🔄 Body Paragraph 2</h5>
                        <p><em><strong>However, some people will continue to buy printed books and newspapers because they provide a better reading experience.</strong> This is because reading from paper is easier on the eyes and helps people concentrate better than reading from screens. <strong>For instance,</strong> many students still prefer printed textbooks when studying for exams because they can make notes and remember information more easily. <strong>Therefore,</strong> printed materials will not disappear completely.</em></p>
                        <small>(76 words)</small>
                    </div>

                    <div class="paragraph-box">
                        <h5>🎯 Conclusion</h5>
                        <p><em>In conclusion, while many people will choose to read online content because it is convenient and free, I believe that printed newspapers and books will not disappear completely. Some people will always prefer the traditional reading experience that printed materials provide.</em></p>
                        <small>(41 words)</small>
                    </div>
                </div>

                <div class="tip-box">
                    <h4>💡 Why This Essay Works for Band 5-6.0</h4>
                    <ul>
                        <li>✅ <strong>Clear structure:</strong> Introduction → Body 1 → Body 2 → Conclusion</li>
                        <li>✅ <strong>Clear opinion:</strong> "I partly disagree"</li>
                        <li>✅ <strong>Good examples:</strong> Young people with smartphones, students with textbooks</li>
                        <li>✅ <strong>Simple linking words:</strong> Firstly, However, For example, Therefore</li>
                        <li>✅ <strong>Appropriate length:</strong> 267 words</li>
                        <li>✅ <strong>Stays on topic:</strong> All ideas relate to the question</li>
                        <li>✅ <strong>Simple but correct grammar:</strong> No complex structures that could cause errors</li>
                    </ul>
                </div>

                <div class="warning-box">
                    <h4>⚠️ Remember</h4>
                    <p>This is just one way to answer the question. Your essay might be different, and that's perfectly fine! The important thing is to follow the structure and include clear reasons with examples.</p>
                </div>

                <div class="navigation-buttons">
                    <button class="prev-btn" onclick="showStep(6)">← Previous</button>
                    <button class="next-btn" onclick="showStep(8)">Next: Vocabulary →</button>
                </div>
            </div>

            <!-- Step 8: Vocabulary -->
            <div class="step" id="step8">
                <h2>📚 Step 8: Useful Vocabulary & Phrases</h2>

                <p>Here are some useful words and phrases for this topic and similar essays:</p>

                <div class="vocabulary-grid">
                    <div class="vocab-card">
                        <h5>📱 Technology & Digital</h5>
                        <ul>
                            <li>online content</li>
                            <li>digital reading</li>
                            <li>electronic devices</li>
                            <li>smartphones/tablets</li>
                            <li>internet access</li>
                            <li>download</li>
                        </ul>
                    </div>

                    <div class="vocab-card">
                        <h5>📰 Print Media</h5>
                        <ul>
                            <li>printed materials</li>
                            <li>traditional books</li>
                            <li>physical newspapers</li>
                            <li>paper-based reading</li>
                            <li>hardcover/paperback</li>
                            <li>publications</li>
                        </ul>
                    </div>

                    <div class="vocab-card">
                        <h5>👥 People & Groups</h5>
                        <ul>
                            <li>young generation</li>
                            <li>elderly people</li>
                            <li>students</li>
                            <li>readers</li>
                            <li>consumers</li>
                            <li>the general public</li>
                        </ul>
                    </div>

                    <div class="vocab-card">
                        <h5>💰 Cost & Convenience</h5>
                        <ul>
                            <li>free of charge</li>
                            <li>cost-effective</li>
                            <li>convenient</li>
                            <li>accessible</li>
                            <li>immediate access</li>
                            <li>affordable</li>
                        </ul>
                    </div>

                    <div class="vocab-card">
                        <h5>👍 Advantages</h5>
                        <ul>
                            <li>easier on the eyes</li>
                            <li>better concentration</li>
                            <li>no eye strain</li>
                            <li>tactile experience</li>
                            <li>reliable</li>
                            <li>portable</li>
                        </ul>
                    </div>

                    <div class="vocab-card">
                        <h5>🔗 Linking Words</h5>
                        <ul>
                            <li>Firstly/Secondly</li>
                            <li>However/Nevertheless</li>
                            <li>For example/For instance</li>
                            <li>As a result/Therefore</li>
                            <li>In addition/Furthermore</li>
                            <li>On the other hand</li>
                        </ul>
                    </div>
                </div>

                <div class="example-box">
                    <h4>📝 Useful Sentence Patterns</h4>
                    <p><strong>Giving opinions:</strong></p>
                    <ul>
                        <li>"I believe that..."</li>
                        <li>"In my opinion..."</li>
                        <li>"I partly agree/disagree because..."</li>
                        <li>"It seems to me that..."</li>
                    </ul>

                    <p><strong>Giving reasons:</strong></p>
                    <ul>
                        <li>"This is because..."</li>
                        <li>"The main reason is..."</li>
                        <li>"One advantage/disadvantage is..."</li>
                        <li>"This leads to..."</li>
                    </ul>

                    <p><strong>Giving examples:</strong></p>
                    <ul>
                        <li>"For example, many people..."</li>
                        <li>"A good example of this is..."</li>
                        <li>"Such as..."</li>
                        <li>"This can be seen in..."</li>
                    </ul>
                </div>

                <div class="tip-box">
                    <h4>💡 Vocabulary Tips for Band 5-6.0</h4>
                    <ul>
                        <li>Use simple words correctly rather than complex words incorrectly</li>
                        <li>Learn 2-3 ways to say the same thing (e.g., "convenient" = "easy to use")</li>
                        <li>Practice linking words in simple sentences first</li>
                        <li>Don't use a word if you're not sure about its meaning</li>
                    </ul>
                </div>

                <div class="navigation-buttons">
                    <button class="prev-btn" onclick="showStep(7)">← Previous</button>
                    <button class="next-btn" onclick="showStep(0)">Start Again →</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 0;
        const totalSteps = 9;

        function showStep(stepNumber) {
            // Hide all steps
            const steps = document.querySelectorAll('.step');
            steps.forEach(step => {
                step.classList.remove('active');
            });

            // Show selected step
            document.getElementById(`step${stepNumber}`).classList.add('active');

            // Update navigation buttons
            const navButtons = document.querySelectorAll('.nav-btn');
            navButtons.forEach((btn, index) => {
                btn.classList.remove('active');
                if (index === stepNumber) {
                    btn.classList.add('active');
                }
            });

            // Update progress bar
            const progressFill = document.getElementById('progressFill');
            const progressPercentage = (stepNumber / (totalSteps - 1)) * 100;
            progressFill.style.width = progressPercentage + '%';

            currentStep = stepNumber;
        }

        function checkAnalysis() {
            const input = document.getElementById('analysis-input').value.trim();
            const feedback = document.getElementById('analysis-feedback');

            if (input.length < 10) {
                showFeedback(feedback, 'Please write more about your thoughts on this topic.', 'negative');
                return;
            }

            const keywords = ['agree', 'disagree', 'think', 'believe', 'because', 'people', 'books', 'online'];
            const hasKeywords = keywords.some(keyword => input.toLowerCase().includes(keyword));

            if (hasKeywords) {
                showFeedback(feedback, 'Great start! You\'re thinking about the topic. Remember to consider both sides of the argument.', 'positive');
            } else {
                showFeedback(feedback, 'Good effort! Try to include your opinion and reasons why you think that way.', 'negative');
            }
        }

        function checkPlan() {
            const input = document.getElementById('plan-input').value.trim();
            const feedback = document.getElementById('plan-feedback');

            if (input.length < 20) {
                showFeedback(feedback, 'Your plan needs more detail. Include your opinion and at least 2 main ideas.', 'negative');
                return;
            }

            const hasOpinion = /agree|disagree|believe|think/i.test(input);
            const hasStructure = /body|paragraph|example|reason/i.test(input);

            if (hasOpinion && hasStructure) {
                showFeedback(feedback, 'Excellent plan! You have a clear opinion and structure. This will help you write a good essay.', 'positive');
            } else if (hasOpinion) {
                showFeedback(feedback, 'Good! You have an opinion. Now add more details about your main reasons and examples.', 'negative');
            } else {
                showFeedback(feedback, 'Your plan needs a clear opinion (agree/disagree) and main reasons.', 'negative');
            }
        }

        function checkIntroduction() {
            const input = document.getElementById('intro-input').value.trim();
            const feedback = document.getElementById('intro-feedback');

            if (input.length < 30) {
                showFeedback(feedback, 'Your introduction is too short. Include a paraphrase of the question and your opinion.', 'negative');
                return;
            }

            const hasParaphrase = /people|future|online|digital|printed|books|newspapers/i.test(input);
            const hasOpinion = /agree|disagree|believe|opinion|view/i.test(input);

            if (hasParaphrase && hasOpinion) {
                showFeedback(feedback, 'Well done! Your introduction paraphrases the question and states your opinion clearly.', 'positive');
            } else if (hasOpinion) {
                showFeedback(feedback, 'Good opinion! Now try to paraphrase the question in your own words.', 'negative');
            } else {
                showFeedback(feedback, 'Remember to include both: paraphrase the question AND state your opinion.', 'negative');
            }
        }

        function checkBodyParagraphs() {
            const body1 = document.getElementById('body1-input').value.trim();
            const body2 = document.getElementById('body2-input').value.trim();
            const feedback = document.getElementById('body-feedback');

            if (body1.length < 50 || body2.length < 50) {
                showFeedback(feedback, 'Your body paragraphs need more development. Each should have a main idea, explanation, and example.', 'negative');
                return;
            }

            const hasLinking1 = /firstly|first|one reason|to begin/i.test(body1);
            const hasLinking2 = /however|secondly|another|on the other hand/i.test(body2);
            const hasExamples = /example|instance|such as|for example/i.test(body1 + body2);

            if (hasLinking1 && hasLinking2 && hasExamples) {
                showFeedback(feedback, 'Excellent body paragraphs! You used good linking words and included examples.', 'positive');
            } else {
                showFeedback(feedback, 'Good effort! Try to include more linking words (Firstly, However) and specific examples.', 'negative');
            }
        }

        function checkConclusion() {
            const input = document.getElementById('conclusion-input').value.trim();
            const feedback = document.getElementById('conclusion-feedback');

            if (input.length < 20) {
                showFeedback(feedback, 'Your conclusion is too short. Summarize your opinion in 2 sentences.', 'negative');
                return;
            }

            const hasConclusion = /conclusion|summarize|overall|believe|opinion/i.test(input);
            const tooLong = input.length > 150;

            if (hasConclusion && !tooLong) {
                showFeedback(feedback, 'Great conclusion! You summarized your opinion without introducing new ideas.', 'positive');
            } else if (tooLong) {
                showFeedback(feedback, 'Your conclusion is too long. Keep it simple - just summarize your main opinion.', 'negative');
            } else {
                showFeedback(feedback, 'Good start! Make sure to clearly summarize your opinion using phrases like "In conclusion..."', 'negative');
            }
        }

        function reviewEssay() {
            const input = document.getElementById('review-input').value.trim();
            const feedback = document.getElementById('review-feedback');

            if (input.length < 100) {
                showFeedback(feedback, 'Please paste your complete essay for review.', 'negative');
                return;
            }

            const wordCount = input.split(/\s+/).length;
            const hasParagraphs = input.split('\n\n').length >= 3;
            const hasOpinion = /agree|disagree|believe|opinion/i.test(input);
            const hasExamples = /example|instance|such as/i.test(input);

            let feedbackText = `Word count: ${wordCount} words. `;

            if (wordCount >= 250 && wordCount <= 300) {
                feedbackText += '✅ Good length! ';
            } else if (wordCount < 250) {
                feedbackText += '⚠️ Too short - aim for 250+ words. ';
            } else {
                feedbackText += '⚠️ Too long - try to be more concise. ';
            }

            if (hasParagraphs) feedbackText += '✅ Good paragraph structure. ';
            if (hasOpinion) feedbackText += '✅ Clear opinion stated. ';
            if (hasExamples) feedbackText += '✅ Examples included. ';

            if (!hasParagraphs) feedbackText += '❌ Use clear paragraphs. ';
            if (!hasOpinion) feedbackText += '❌ State your opinion clearly. ';
            if (!hasExamples) feedbackText += '❌ Add specific examples. ';

            const isGood = wordCount >= 250 && wordCount <= 300 && hasParagraphs && hasOpinion && hasExamples;
            showFeedback(feedback, feedbackText, isGood ? 'positive' : 'negative');
        }

        function showFeedback(element, message, type) {
            element.textContent = message;
            element.className = `feedback show ${type}`;
        }

        // Initialize the lesson
        document.addEventListener('DOMContentLoaded', function() {
            showStep(0);
        });

        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft' && currentStep > 0) {
                showStep(currentStep - 1);
            } else if (e.key === 'ArrowRight' && currentStep < totalSteps - 1) {
                showStep(currentStep + 1);
            }
        });
    </script>
</body>
</html>